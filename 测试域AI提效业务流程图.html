<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试域AI提效泳道图</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', <PERSON>l, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
        }

        .container {
            max-width: 1800px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 15px 50px rgba(0,0,0,0.1);
            padding: 30px;
            overflow-x: auto;
        }

        .title {
            text-align: center;
            color: #2c3e50;
            font-size: 28px;
            font-weight: bold;
            margin-bottom: 30px;
            background: linear-gradient(45deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        /* 泳道图主体 */
        .swimlane-diagram {
            display: grid;
            grid-template-columns: 200px repeat(5, 1fr);
            grid-template-rows: repeat(4, auto);
            gap: 2px;
            background: #ecf0f1;
            border-radius: 10px;
            padding: 2px;
            min-width: 1400px;
        }

        /* 泳道标题列 */
        .lane-header {
            background: linear-gradient(135deg, #34495e 0%, #2c3e50 100%);
            color: white;
            padding: 25px 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 16px;
            text-align: center;
            border-radius: 8px;
            writing-mode: horizontal-tb;
        }

        /* 流程阶段标题行 */
        .phase-header {
            background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
            color: white;
            padding: 20px;
            text-align: center;
            font-weight: bold;
            font-size: 14px;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        /* 泳道单元格 */
        .lane-cell {
            background: white;
            padding: 15px;
            border-radius: 8px;
            min-height: 120px;
            position: relative;
            transition: all 0.3s ease;
            border: 2px solid transparent;
        }

        .lane-cell:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
            border-color: #3498db;
        }

        /* 用户触点层样式 */
        .user-lane .lane-cell {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        /* 上下文工程层样式 */
        .context-lane .lane-cell {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            color: white;
        }

        /* 业务流程层样式 */
        .business-lane .lane-cell {
            background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
            color: #2c3e50;
        }

        /* LLM基础设施层样式 */
        .llm-lane .lane-cell {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
        }

        .cell-title {
            font-weight: bold;
            font-size: 14px;
            margin-bottom: 10px;
            border-bottom: 1px solid rgba(255,255,255,0.3);
            padding-bottom: 5px;
        }

        .business-lane .cell-title {
            border-bottom-color: rgba(44,62,80,0.3);
        }

        .cell-content {
            font-size: 11px;
            line-height: 1.4;
            opacity: 0.9;
        }

        .business-lane .cell-content {
            opacity: 0.8;
            color: #34495e;
        }

        /* 流程箭头 */
        .flow-arrow {
            position: absolute;
            right: -12px;
            top: 50%;
            transform: translateY(-50%);
            width: 0;
            height: 0;
            border-left: 15px solid #3498db;
            border-top: 10px solid transparent;
            border-bottom: 10px solid transparent;
            z-index: 10;
        }

        .lane-cell:last-child .flow-arrow {
            display: none;
        }

        /* 交互连接线 */
        .interaction-line {
            position: absolute;
            width: 2px;
            background: #e74c3c;
            z-index: 5;
        }

        /* 关键依赖标识 */
        .dependency {
            background: rgba(231, 76, 60, 0.1);
            border: 1px dashed #e74c3c;
            border-radius: 4px;
            padding: 3px 6px;
            font-size: 10px;
            color: #e74c3c;
            margin-top: 5px;
            display: inline-block;
        }

        /* AI提效标识 */
        .ai-enhancement {
            position: absolute;
            top: -8px;
            right: -8px;
            background: #f39c12;
            color: white;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 10px;
            font-weight: bold;
        }

        /* 图例 */
        .legend {
            margin-top: 30px;
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            border: 1px solid #dee2e6;
        }

        .legend-title {
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 15px;
            font-size: 16px;
        }

        .legend-items {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }

        .legend-item {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .legend-color {
            width: 20px;
            height: 20px;
            border-radius: 4px;
        }

        .legend-text {
            font-size: 14px;
            color: #495057;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="title">测试域AI提效泳道图</div>

        <!-- 泳道图主体 -->
        <div class="swimlane-diagram">
            <!-- 空白角落 -->
            <div class="phase-header" style="background: #95a5a6;">层级/阶段</div>

            <!-- 流程阶段标题 -->
            <div class="phase-header">测试设计</div>
            <div class="phase-header">自动化开发</div>
            <div class="phase-header">自动化部署</div>
            <div class="phase-header">测试执行</div>
            <div class="phase-header">故障复盘</div>

            <!-- 用户触点层 -->
            <div class="lane-header">用户触点层<br><small>个人智能体平台</small></div>
            <div class="lane-cell user-lane">
                <div class="cell-title">🖥️ IDE交互</div>
                <div class="cell-content">
                    • Cursor智能编程<br>
                    • AI IDE集成开发<br>
                    • 需求输入与澄清
                </div>
                <div class="ai-enhancement">AI</div>
                <div class="flow-arrow"></div>
            </div>
            <div class="lane-cell user-lane">
                <div class="cell-title">💬 对话交互</div>
                <div class="cell-content">
                    • Cherry Studio对话<br>
                    • ChatBox交互<br>
                    • 脚本需求描述
                </div>
                <div class="ai-enhancement">AI</div>
                <div class="flow-arrow"></div>
            </div>
            <div class="lane-cell user-lane">
                <div class="cell-title">📊 监控界面</div>
                <div class="cell-content">
                    • 部署状态监控<br>
                    • 日志实时查看<br>
                    • 故障告警接收
                </div>
                <div class="ai-enhancement">AI</div>
                <div class="flow-arrow"></div>
            </div>
            <div class="lane-cell user-lane">
                <div class="cell-title">🎯 执行控制</div>
                <div class="cell-content">
                    • 测试策略调整<br>
                    • 执行进度跟踪<br>
                    • 结果实时反馈
                </div>
                <div class="ai-enhancement">AI</div>
                <div class="flow-arrow"></div>
            </div>
            <div class="lane-cell user-lane">
                <div class="cell-title">📈 复盘分析</div>
                <div class="cell-content">
                    • 问题根因分析<br>
                    • 改进建议获取<br>
                    • 经验知识沉淀
                </div>
                <div class="ai-enhancement">AI</div>
            </div>

            <!-- 上下文工程层 -->
            <div class="lane-header">上下文工程层<br><small>智能信息处理</small></div>
            <div class="lane-cell context-lane">
                <div class="cell-title">📝 提示词工程</div>
                <div class="cell-content">
                    • 用例设计模板<br>
                    • 评审标准提示<br>
                    • 用户个性化提示
                </div>
                <div class="flow-arrow"></div>
            </div>
            <div class="lane-cell context-lane">
                <div class="cell-title">🧠 记忆与画像</div>
                <div class="cell-content">
                    • 开发习惯记忆<br>
                    • 技术栈偏好<br>
                    • 历史脚本模式
                    <div class="dependency">用例澄清</div>
                </div>
                <div class="flow-arrow"></div>
            </div>
            <div class="lane-cell context-lane">
                <div class="cell-title">🔍 检索增强</div>
                <div class="cell-content">
                    • 部署配置检索<br>
                    • 历史日志分析<br>
                    • 故障案例库
                </div>
                <div class="flow-arrow"></div>
            </div>
            <div class="lane-cell context-lane">
                <div class="cell-title">🛠️ MCP工具链</div>
                <div class="cell-content">
                    • iCenter MCP<br>
                    • RDC MCP<br>
                    • 自动化框架MCP
                    <div class="dependency">用例澄清</div>
                </div>
                <div class="flow-arrow"></div>
            </div>
            <div class="lane-cell context-lane">
                <div class="cell-title">💾 知识沉淀</div>
                <div class="cell-content">
                    • 故障模式总结<br>
                    • 解决方案归档<br>
                    • 最佳实践提取
                </div>
            </div>

            <!-- 业务流程层 -->
            <div class="lane-header">业务流程层<br><small>测试域核心流程</small></div>
            <div class="lane-cell business-lane">
                <div class="cell-title">📋 测试设计</div>
                <div class="cell-content">
                    • 需求分析<br>
                    • 需求评审<br>
                    • 方案设计<br>
                    • 方案评审<br>
                    • 用例设计<br>
                    • 用例评审<br>
                    • 用例归档<br>
                    • 用例澄清
                </div>
                <div class="flow-arrow"></div>
            </div>
            <div class="lane-cell business-lane">
                <div class="cell-title">⚙️ 自动化开发</div>
                <div class="cell-content">
                    • 脚本生成<br>
                    • 脚本调试<br>
                    • 脚本提交<br>
                    <div class="dependency">依赖用例澄清</div>
                </div>
                <div class="flow-arrow"></div>
            </div>
            <div class="lane-cell business-lane">
                <div class="cell-title">🚀 自动化部署</div>
                <div class="cell-content">
                    • 脚本部署<br>
                    • 日志分析<br>
                    • 故障提交与闭环
                </div>
                <div class="flow-arrow"></div>
            </div>
            <div class="lane-cell business-lane">
                <div class="cell-title">🎯 测试执行</div>
                <div class="cell-content">
                    • 智能测试策略<br>
                    • 手工执行<br>
                    • 日志分析<br>
                    • 测试报告生成<br>
                    • 故障提交与闭环<br>
                    • 智能数据分析
                    <div class="dependency">依赖用例澄清</div>
                </div>
                <div class="flow-arrow"></div>
            </div>
            <div class="lane-cell business-lane">
                <div class="cell-title">🔄 故障复盘</div>
                <div class="cell-content">
                    • 问题分析<br>
                    • 根因定位<br>
                    • 改进措施<br>
                    • 经验沉淀
                </div>
            </div>

            <!-- LLM基础设施层 -->
            <div class="lane-header">LLM基础设施层<br><small>AI能力底座</small></div>
            <div class="lane-cell llm-lane">
                <div class="cell-title">🤖 模型服务</div>
                <div class="cell-content">
                    • 星云大模型<br>
                    • 电信大模型<br>
                    • Qwen系列<br>
                    • 测试域小模型
                </div>
                <div class="flow-arrow"></div>
            </div>
            <div class="lane-cell llm-lane">
                <div class="cell-title">⚙️ 模型调优</div>
                <div class="cell-content">
                    • 精调优化<br>
                    • 量化压缩<br>
                    • 模型蒸馏<br>
                    • 强化学习
                </div>
                <div class="flow-arrow"></div>
            </div>
            <div class="lane-cell llm-lane">
                <div class="cell-title">📊 性能监控</div>
                <div class="cell-content">
                    • 推理延迟<br>
                    • 资源使用率<br>
                    • 服务可用性<br>
                    • 质量评估
                </div>
                <div class="flow-arrow"></div>
            </div>
            <div class="lane-cell llm-lane">
                <div class="cell-title">🎯 智能分析</div>
                <div class="cell-content">
                    • 测试结果分析<br>
                    • 异常模式识别<br>
                    • 预测性维护<br>
                    • 智能推荐
                </div>
                <div class="flow-arrow"></div>
            </div>
            <div class="lane-cell llm-lane">
                <div class="cell-title">📈 评估优化</div>
                <div class="cell-content">
                    • F1 Score评估<br>
                    • Accuracy测量<br>
                    • 莱文斯坦距离<br>
                    • 持续改进
                </div>
            </div>
        </div>

        <!-- 图例说明 -->
        <div class="legend">
            <div class="legend-title">🔍 图例说明</div>
            <div class="legend-items">
                <div class="legend-item">
                    <div class="legend-color" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);"></div>
                    <div class="legend-text">用户触点层 - 人机交互界面</div>
                </div>
                <div class="legend-item">
                    <div class="legend-color" style="background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);"></div>
                    <div class="legend-text">上下文工程层 - 智能信息处理</div>
                </div>
                <div class="legend-item">
                    <div class="legend-color" style="background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);"></div>
                    <div class="legend-text">业务流程层 - 测试域核心流程</div>
                </div>
                <div class="legend-item">
                    <div class="legend-color" style="background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);"></div>
                    <div class="legend-text">LLM基础设施层 - AI能力底座</div>
                </div>
                <div class="legend-item">
                    <div class="legend-color" style="background: #f39c12; border-radius: 50%;"></div>
                    <div class="legend-text">AI智能提效标识</div>
                </div>
                <div class="legend-item">
                    <div class="legend-color" style="background: rgba(231, 76, 60, 0.1); border: 1px dashed #e74c3c;"></div>
                    <div class="legend-text">关键依赖关系</div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
