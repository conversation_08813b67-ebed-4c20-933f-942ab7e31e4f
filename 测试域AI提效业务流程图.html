<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试域AI提效架构图</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', <PERSON>l, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }

        .container {
            max-width: 1600px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 60px rgba(0,0,0,0.2);
            padding: 40px;
            position: relative;
        }

        .title {
            text-align: center;
            color: #2c3e50;
            font-size: 32px;
            font-weight: bold;
            margin-bottom: 40px;
            background: linear-gradient(45deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        /* 架构层级布局 */
        .architecture {
            display: grid;
            grid-template-rows: auto auto auto auto;
            gap: 30px;
            position: relative;
        }

        /* 用户层 */
        .user-tier {
            display: flex;
            justify-content: center;
            gap: 20px;
        }

        .user-interface {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px 30px;
            border-radius: 15px;
            text-align: center;
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
            min-width: 120px;
        }

        .interface-title {
            font-size: 14px;
            font-weight: bold;
            margin-bottom: 8px;
        }

        .interface-subtitle {
            font-size: 12px;
            opacity: 0.9;
        }

        /* 上下文工程层 */
        .context-tier {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            border-radius: 20px;
            padding: 30px;
            color: white;
            position: relative;
        }

        .context-title {
            text-align: center;
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 25px;
            border-bottom: 2px solid rgba(255,255,255,0.3);
            padding-bottom: 10px;
        }

        .context-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 20px;
        }

        .context-item {
            background: rgba(255,255,255,0.15);
            padding: 15px;
            border-radius: 12px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.2);
        }

        .context-item-title {
            font-weight: bold;
            font-size: 14px;
            margin-bottom: 8px;
            color: #fff;
        }

        .context-item-content {
            font-size: 12px;
            line-height: 1.4;
            opacity: 0.9;
        }

        /* 业务流程层 */
        .business-tier {
            background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
            border-radius: 20px;
            padding: 30px;
            position: relative;
        }

        .business-title {
            text-align: center;
            font-size: 24px;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 25px;
        }

        .business-flow {
            display: flex;
            justify-content: space-between;
            align-items: center;
            position: relative;
        }

        .flow-node {
            background: white;
            border: 3px solid #3498db;
            border-radius: 15px;
            padding: 20px;
            text-align: center;
            width: 180px;
            position: relative;
            box-shadow: 0 8px 25px rgba(52, 152, 219, 0.2);
            transition: all 0.3s ease;
        }

        .flow-node:hover {
            transform: translateY(-8px);
            box-shadow: 0 15px 35px rgba(52, 152, 219, 0.3);
        }

        .node-title {
            font-weight: bold;
            color: #2c3e50;
            font-size: 16px;
            margin-bottom: 12px;
            border-bottom: 2px solid #3498db;
            padding-bottom: 8px;
        }

        .node-content {
            font-size: 11px;
            color: #7f8c8d;
            line-height: 1.3;
        }

        .flow-arrow {
            width: 40px;
            height: 3px;
            background: #3498db;
            position: relative;
            margin: 0 10px;
        }

        .flow-arrow::after {
            content: '';
            position: absolute;
            right: -8px;
            top: -5px;
            width: 0;
            height: 0;
            border-left: 12px solid #3498db;
            border-top: 6px solid transparent;
            border-bottom: 6px solid transparent;
        }

        /* LLM基础设施层 */
        .llm-tier {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            border-radius: 20px;
            padding: 30px;
            color: white;
        }

        .llm-title {
            text-align: center;
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 25px;
            border-bottom: 2px solid rgba(255,255,255,0.3);
            padding-bottom: 10px;
        }

        .llm-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 25px;
        }

        .llm-component {
            background: rgba(255,255,255,0.15);
            padding: 20px;
            border-radius: 15px;
            text-align: center;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.2);
        }

        .component-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 15px;
            color: #fff;
        }

        .component-content {
            font-size: 13px;
            line-height: 1.5;
            opacity: 0.9;
        }

        /* 连接线 */
        .connection-line {
            position: absolute;
            left: 50%;
            transform: translateX(-50%);
            width: 3px;
            background: linear-gradient(to bottom, #667eea, #f093fb, #a8edea, #4facfe);
            z-index: -1;
        }

        .line-1 { top: 120px; height: 50px; }
        .line-2 { top: 250px; height: 50px; }
        .line-3 { top: 450px; height: 50px; }

        /* AI提效标识 */
        .ai-badge {
            position: absolute;
            top: 20px;
            right: 30px;
            background: linear-gradient(45deg, #ff6b6b, #feca57);
            color: white;
            padding: 10px 20px;
            border-radius: 25px;
            font-size: 14px;
            font-weight: bold;
            box-shadow: 0 4px 15px rgba(255, 107, 107, 0.3);
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="title">测试域AI提效架构图</div>
        <div class="ai-badge">AI智能提效</div>

        <!-- 架构层级 -->
        <div class="architecture">
            <!-- 连接线 -->
            <div class="connection-line line-1"></div>
            <div class="connection-line line-2"></div>
            <div class="connection-line line-3"></div>

            <!-- 用户触点层 -->
            <div class="user-tier">
                <div class="user-interface">
                    <div class="interface-title">Cursor</div>
                    <div class="interface-subtitle">智能编程</div>
                </div>
                <div class="user-interface">
                    <div class="interface-title">AI IDE</div>
                    <div class="interface-subtitle">集成开发</div>
                </div>
                <div class="user-interface">
                    <div class="interface-title">Cherry Studio</div>
                    <div class="interface-subtitle">对话平台</div>
                </div>
                <div class="user-interface">
                    <div class="interface-title">ChatBox</div>
                    <div class="interface-subtitle">交互工具</div>
                </div>
            </div>

            <!-- 上下文工程层 -->
            <div class="context-tier">
                <div class="context-title">上下文工程层 - 核心信息处理引擎</div>
                <div class="context-grid">
                    <div class="context-item">
                        <div class="context-item-title">提示词工程</div>
                        <div class="context-item-content">
                            • 用户提示词<br>
                            • 通用提示词<br>
                            • 场景化模板
                        </div>
                    </div>
                    <div class="context-item">
                        <div class="context-item-title">用户画像</div>
                        <div class="context-item-content">
                            • 个人偏好<br>
                            • 工作习惯<br>
                            • 技能水平
                        </div>
                    </div>
                    <div class="context-item">
                        <div class="context-item-title">记忆系统</div>
                        <div class="context-item-content">
                            • 短期记忆<br>
                            • 长期记忆<br>
                            • 对话历史
                        </div>
                    </div>
                    <div class="context-item">
                        <div class="context-item-title">检索增强</div>
                        <div class="context-item-content">
                            • 网页检索<br>
                            • RAG知识库<br>
                            • 文档索引
                        </div>
                    </div>
                    <div class="context-item">
                        <div class="context-item-title">MCP工具链</div>
                        <div class="context-item-content">
                            • SequentialThinking<br>
                            • Memory管理<br>
                            • FileSystem操作
                        </div>
                    </div>
                    <div class="context-item">
                        <div class="context-item-title">测试域MCP</div>
                        <div class="context-item-content">
                            • iCenter MCP<br>
                            • RDC MCP<br>
                            • 自动化框架MCP
                        </div>
                    </div>
                    <div class="context-item">
                        <div class="context-item-title">多模态输入</div>
                        <div class="context-item-content">
                            • 文本对话<br>
                            • 文件上传<br>
                            • 图片识别
                        </div>
                    </div>
                    <div class="context-item">
                        <div class="context-item-title">知识沉淀</div>
                        <div class="context-item-content">
                            • 私域知识<br>
                            • 经验共享<br>
                            • 持续学习
                        </div>
                    </div>
                </div>
            </div>

            <!-- 测试域业务流程层 -->
            <div class="business-tier">
                <div class="business-title">测试域端到端业务流程</div>
                <div class="business-flow">
                    <div class="flow-node">
                        <div class="node-title">测试设计</div>
                        <div class="node-content">
                            • 需求分析<br>
                            • 需求评审<br>
                            • 方案设计<br>
                            • 方案评审<br>
                            • 用例设计<br>
                            • 用例评审<br>
                            • 用例归档<br>
                            • 用例澄清
                        </div>
                    </div>

                    <div class="flow-arrow"></div>

                    <div class="flow-node">
                        <div class="node-title">自动化开发</div>
                        <div class="node-content">
                            • 脚本生成<br>
                            • 脚本调试<br>
                            • 脚本提交<br>
                            <br>
                            <strong style="color: #e74c3c;">↑需要用例澄清</strong>
                        </div>
                    </div>

                    <div class="flow-arrow"></div>

                    <div class="flow-node">
                        <div class="node-title">自动化部署</div>
                        <div class="node-content">
                            • 脚本部署<br>
                            • 日志分析<br>
                            • 故障提交与闭环<br>
                            <br>
                            <strong style="color: #27ae60;">共享日志分析</strong>
                        </div>
                    </div>

                    <div class="flow-arrow"></div>

                    <div class="flow-node">
                        <div class="node-title">测试执行</div>
                        <div class="node-content">
                            • 智能测试策略<br>
                            • 手工执行<br>
                            • 日志分析<br>
                            • 测试报告生成<br>
                            • 故障提交与闭环<br>
                            • 智能数据分析<br>
                            <strong style="color: #e74c3c;">↑需要用例澄清</strong>
                        </div>
                    </div>

                    <div class="flow-arrow"></div>

                    <div class="flow-node">
                        <div class="node-title">故障复盘</div>
                        <div class="node-content">
                            • 问题分析<br>
                            • 根因定位<br>
                            • 改进措施<br>
                            • 经验沉淀<br>
                            <br>
                            <strong style="color: #8e44ad;">反馈优化</strong>
                        </div>
                    </div>
                </div>
            </div>

            <!-- LLM基础设施层 -->
            <div class="llm-tier">
                <div class="llm-title">LLM基础设施层</div>
                <div class="llm-grid">
                    <div class="llm-component">
                        <div class="component-title">🤖 模型矩阵</div>
                        <div class="component-content">
                            • 星云大模型<br>
                            • 电信大模型<br>
                            • Qwen系列<br>
                            • Deepseek系列<br>
                            • 测试域专用小模型
                        </div>
                    </div>
                    <div class="llm-component">
                        <div class="component-title">⚙️ 模型调优</div>
                        <div class="component-content">
                            • 精调(Fine-tuning)<br>
                            • 量化压缩<br>
                            • 模型蒸馏<br>
                            • 强化学习(RLHF)<br>
                            • Post-pretraining
                        </div>
                    </div>
                    <div class="llm-component">
                        <div class="component-title">📊 评估体系</div>
                        <div class="component-content">
                            • 莱文斯坦距离<br>
                            • F1 Score<br>
                            • Recall召回率<br>
                            • Accuracy准确率<br>
                            • Loss损失函数
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
