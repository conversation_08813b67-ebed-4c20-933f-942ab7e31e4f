<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试域AI提效业务流程图</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            padding: 30px;
        }
        
        .title {
            text-align: center;
            color: #2c3e50;
            font-size: 28px;
            font-weight: bold;
            margin-bottom: 30px;
            border-bottom: 3px solid #3498db;
            padding-bottom: 15px;
        }
        
        .flow-diagram {
            display: grid;
            grid-template-columns: 1fr 2fr 1fr;
            gap: 30px;
            margin-bottom: 40px;
        }
        
        .user-layer {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 12px;
            text-align: center;
        }
        
        .context-layer {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            color: white;
            padding: 20px;
            border-radius: 12px;
        }
        
        .llm-layer {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 20px;
            border-radius: 12px;
            text-align: center;
        }
        
        .layer-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 15px;
            text-align: center;
            border-bottom: 2px solid rgba(255,255,255,0.3);
            padding-bottom: 8px;
        }
        
        .layer-content {
            font-size: 14px;
            line-height: 1.6;
        }
        
        .business-flow {
            display: grid;
            grid-template-columns: repeat(5, 1fr);
            gap: 20px;
            margin-top: 30px;
        }
        
        .flow-step {
            background: white;
            border: 2px solid #3498db;
            border-radius: 10px;
            padding: 15px;
            text-align: center;
            position: relative;
            transition: all 0.3s ease;
        }
        
        .flow-step:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(52, 152, 219, 0.3);
        }
        
        .step-title {
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 10px;
            font-size: 16px;
        }
        
        .step-content {
            font-size: 12px;
            color: #7f8c8d;
            line-height: 1.4;
        }
        
        .arrow {
            position: absolute;
            right: -15px;
            top: 50%;
            transform: translateY(-50%);
            width: 0;
            height: 0;
            border-left: 10px solid #3498db;
            border-top: 8px solid transparent;
            border-bottom: 8px solid transparent;
        }
        
        .flow-step:last-child .arrow {
            display: none;
        }
        
        .ai-enhancement {
            background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
            border-radius: 12px;
            padding: 20px;
            margin-top: 30px;
        }
        
        .enhancement-title {
            font-size: 20px;
            font-weight: bold;
            color: #2c3e50;
            text-align: center;
            margin-bottom: 20px;
        }
        
        .enhancement-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 20px;
        }
        
        .enhancement-item {
            background: white;
            padding: 15px;
            border-radius: 8px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }
        
        .enhancement-item h4 {
            color: #e74c3c;
            margin-bottom: 10px;
            font-size: 14px;
        }
        
        .enhancement-item p {
            font-size: 12px;
            color: #7f8c8d;
            line-height: 1.4;
            margin: 0;
        }
        
        .feedback-loop {
            background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
            border-radius: 12px;
            padding: 20px;
            margin-top: 20px;
            text-align: center;
        }
        
        .feedback-title {
            font-size: 18px;
            font-weight: bold;
            color: #d35400;
            margin-bottom: 15px;
        }
        
        .feedback-content {
            font-size: 14px;
            color: #8e44ad;
            line-height: 1.6;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="title">测试域AI提效业务流程图</div>
        
        <!-- AI提效架构层 -->
        <div class="flow-diagram">
            <div class="user-layer">
                <div class="layer-title">用户触点层</div>
                <div class="layer-content">
                    • Cursor<br>
                    • AI IDE<br>
                    • Cherry Studio<br>
                    • ChatBox<br>
                    <br>
                    <strong>个人通用智能体平台</strong>
                </div>
            </div>
            
            <div class="context-layer">
                <div class="layer-title">上下文工程层</div>
                <div class="layer-content">
                    <strong>提示词Prompt:</strong> 用户提示词 + 通用提示词<br>
                    <strong>用户画像:</strong> 个人偏好与习惯<br>
                    <strong>短期记忆:</strong> 历史对话摘要<br>
                    <strong>长期记忆:</strong> 用户偏好沉淀<br>
                    <strong>检索信息:</strong> 网页检索结果<br>
                    <strong>RAG信息:</strong> 知识库检索<br>
                    <strong>MCP信息:</strong> SequentialThinking、Memory、FileSystem、iCenter MCP、RDC MCP、自动化框架MCP等
                </div>
            </div>
            
            <div class="llm-layer">
                <div class="layer-title">LLM基础设施层</div>
                <div class="layer-content">
                    <strong>模型:</strong><br>
                    星云、电信、Qwen、Deepseek、测试域小模型<br><br>
                    <strong>调优:</strong><br>
                    精调、量化、模型蒸馏、强化学习、post-pretraining<br><br>
                    <strong>评估:</strong><br>
                    莱文斯坦比、F1、Recall、Accuracy、Loss
                </div>
            </div>
        </div>
        
        <!-- 测试域端到端业务流程 -->
        <div class="business-flow">
            <div class="flow-step">
                <div class="step-title">测试设计</div>
                <div class="step-content">
                    • 需求分析<br>
                    • 需求评审<br>
                    • 方案设计<br>
                    • 方案评审<br>
                    • 用例设计<br>
                    • 用例评审<br>
                    • 用例归档<br>
                    • 用例澄清
                </div>
                <div class="arrow"></div>
            </div>
            
            <div class="flow-step">
                <div class="step-title">自动化开发</div>
                <div class="step-content">
                    • 脚本生成<br>
                    • 脚本调试<br>
                    • 脚本提交<br>
                    <br>
                    <strong style="color: #e74c3c;">需要用例澄清</strong>
                </div>
                <div class="arrow"></div>
            </div>
            
            <div class="flow-step">
                <div class="step-title">自动化部署</div>
                <div class="step-content">
                    • 脚本部署<br>
                    • 日志分析<br>
                    • 故障提交与闭环
                </div>
                <div class="arrow"></div>
            </div>
            
            <div class="flow-step">
                <div class="step-title">测试执行</div>
                <div class="step-content">
                    • 智能测试策略<br>
                    • 手工执行<br>
                    • 日志分析<br>
                    • 测试报告生成<br>
                    • 故障提交与闭环<br>
                    • 智能数据分析<br>
                    <strong style="color: #e74c3c;">需要用例澄清</strong>
                </div>
                <div class="arrow"></div>
            </div>
            
            <div class="flow-step">
                <div class="step-title">故障复盘</div>
                <div class="step-content">
                    • 问题分析<br>
                    • 根因定位<br>
                    • 改进措施<br>
                    • 经验沉淀
                </div>
            </div>
        </div>
        
        <!-- AI提效场景 -->
        <div class="ai-enhancement">
            <div class="enhancement-title">测试域场景AI提效新范式</div>
            <div class="enhancement-grid">
                <div class="enhancement-item">
                    <h4>单点提效</h4>
                    <p>针对特定环节进行AI增强，如用例设计自动化、脚本生成优化、日志智能分析等</p>
                </div>
                <div class="enhancement-item">
                    <h4>多点提效</h4>
                    <p>跨环节协同优化，如用例设计+用例评审、自动化开发+部署联动等</p>
                </div>
                <div class="enhancement-item">
                    <h4>全流程提效</h4>
                    <p>端到端智能化，从需求分析到故障复盘的全链路AI赋能</p>
                </div>
            </div>
        </div>
        
        <!-- 用户交互与反馈循环 -->
        <div class="feedback-loop">
            <div class="feedback-title">用户交互与持续优化</div>
            <div class="feedback-content">
                用户通过IDE对话交互，可随时介入调教大模型输出 → 通过对话、文件、图片等多模态方式补充上下文 → 
                形成用户画像和长短期记忆 → 私域知识共享 → 持续优化AI输出质量
            </div>
        </div>
    </div>
</body>
</html>
